import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../hooks/useTheme';
import Button from '../components/Button';

interface Address {
  id: string;
  type: 'home' | 'work' | 'other';
  title: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
}

const ManageAddressesScreen = () => {
  const navigation = useNavigation();
  const { isDark } = useTheme();
  
  // Mock data - in real app, this would come from API/state management
  const [addresses, setAddresses] = useState<Address[]>([
    {
      id: '1',
      type: 'home',
      title: 'Home',
      address: '123 Main Street, Apt 4B',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      isDefault: true
    },
    {
      id: '2',
      type: 'work',
      title: 'Office',
      address: '456 Business Ave, Suite 200',
      city: 'New York',
      state: 'NY',
      zipCode: '10002',
      isDefault: false
    }
  ]);

  const handleAddAddress = () => {
    Alert.alert('Add Address', 'Address form will open here');
  };

  const handleEditAddress = (address: Address) => {
    Alert.alert('Edit Address', `Editing ${address.title}`);
  };

  const handleDeleteAddress = (addressId: string) => {
    Alert.alert(
      'Delete Address',
      'Are you sure you want to delete this address?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setAddresses(prev => prev.filter(addr => addr.id !== addressId));
          }
        }
      ]
    );
  };

  const handleSetDefault = (addressId: string) => {
    setAddresses(prev => 
      prev.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      }))
    );
  };

  const getAddressIcon = (type: string) => {
    switch (type) {
      case 'home':
        return 'home';
      case 'work':
        return 'business';
      default:
        return 'location';
    }
  };

  const renderAddressItem = ({ item }: { item: Address }) => (
    <View className={`rounded-2xl p-4 mb-3 ${isDark ? 'bg-neutral-800' : 'bg-white'} shadow-sm border ${isDark ? 'border-neutral-700' : 'border-neutral-100'}`}>
      <View className="flex-row items-start justify-between mb-3">
        <View className="flex-row items-center flex-1">
          <View className={`w-10 h-10 rounded-full items-center justify-center mr-3 ${isDark ? 'bg-neutral-700' : 'bg-neutral-100'}`}>
            <Ionicons 
              name={getAddressIcon(item.type) as any} 
              size={20} 
              color={isDark ? '#d4d4d4' : '#64748b'} 
            />
          </View>
          <View className="flex-1">
            <View className="flex-row items-center">
              <Text className={`text-lg font-semibold ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
                {item.title}
              </Text>
              {item.isDefault && (
                <View className="ml-2 px-2 py-1 bg-primary-100 rounded-full">
                  <Text className="text-primary-700 text-xs font-medium">Default</Text>
                </View>
              )}
            </View>
            <Text className={`text-sm mt-1 ${isDark ? 'text-neutral-300' : 'text-neutral-600'}`}>
              {item.address}
            </Text>
            <Text className={`text-sm ${isDark ? 'text-neutral-400' : 'text-neutral-500'}`}>
              {item.city}, {item.state} {item.zipCode}
            </Text>
          </View>
        </View>
      </View>

      <View className="flex-row space-x-2">
        {!item.isDefault && (
          <TouchableOpacity
            onPress={() => handleSetDefault(item.id)}
            className={`flex-1 py-2 px-3 rounded-lg border ${isDark ? 'border-neutral-600 bg-neutral-700' : 'border-neutral-300 bg-neutral-50'}`}
          >
            <Text className={`text-center text-sm font-medium ${isDark ? 'text-neutral-200' : 'text-neutral-700'}`}>
              Set Default
            </Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          onPress={() => handleEditAddress(item)}
          className="flex-1 py-2 px-3 rounded-lg bg-primary-100 border border-primary-200"
        >
          <Text className="text-center text-sm font-medium text-primary-700">
            Edit
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={() => handleDeleteAddress(item.id)}
          className={`py-2 px-3 rounded-lg border ${isDark ? 'border-error-800 bg-error-900/20' : 'border-error-200 bg-error-50'}`}
        >
          <Ionicons name="trash-outline" size={16} color="#ef4444" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-neutral-900' : 'bg-neutral-50'}`}>
      {/* Header */}
      <View className={`px-4 py-4 border-b ${isDark ? 'bg-neutral-800 border-neutral-700' : 'bg-white border-neutral-200'}`}>
        <View className="flex-row items-center justify-between">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="w-10 h-10 items-center justify-center"
          >
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={isDark ? '#f5f5f5' : '#1e293b'} 
            />
          </TouchableOpacity>
          <Text className={`text-lg font-bold ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
            Manage Addresses
          </Text>
          <TouchableOpacity
            onPress={handleAddAddress}
            className="w-10 h-10 items-center justify-center"
          >
            <Ionicons 
              name="add" 
              size={24} 
              color={isDark ? '#f5f5f5' : '#1e293b'} 
            />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1 px-4 py-6" showsVerticalScrollIndicator={false}>
        {/* Info Card */}
        <View className={`rounded-2xl p-4 mb-6 ${isDark ? 'bg-info-900/20 border-info-800' : 'bg-info-50 border-info-200'} border`}>
          <View className="flex-row items-start">
            <Ionicons 
              name="information-circle" 
              size={20} 
              color={isDark ? '#60a5fa' : '#3b82f6'} 
              style={{ marginTop: 2 }} 
            />
            <View className="flex-1 ml-3">
              <Text className={`text-sm font-medium ${isDark ? 'text-info-200' : 'text-info-800'}`}>
                Delivery Information
              </Text>
              <Text className={`text-sm mt-1 ${isDark ? 'text-info-300' : 'text-info-700'}`}>
                Add multiple addresses for faster checkout. Your default address will be pre-selected during orders.
              </Text>
            </View>
          </View>
        </View>

        {/* Addresses List */}
        {addresses.length > 0 ? (
          <FlatList
            data={addresses}
            renderItem={renderAddressItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View className="items-center py-12">
            <View className={`w-20 h-20 rounded-full items-center justify-center mb-4 ${isDark ? 'bg-neutral-800' : 'bg-neutral-100'}`}>
              <Ionicons 
                name="location-outline" 
                size={32} 
                color={isDark ? '#a3a3a3' : '#64748b'} 
              />
            </View>
            <Text className={`text-lg font-semibold mb-2 ${isDark ? 'text-neutral-200' : 'text-neutral-800'}`}>
              No Addresses Added
            </Text>
            <Text className={`text-center mb-6 ${isDark ? 'text-neutral-400' : 'text-neutral-600'}`}>
              Add your first delivery address to get started with orders.
            </Text>
            <Button
              title="Add Address"
              onPress={handleAddAddress}
              variant="primary"
              size="md"
            />
          </View>
        )}

        {/* Add Address Button (when addresses exist) */}
        {addresses.length > 0 && (
          <Button
            title="Add New Address"
            onPress={handleAddAddress}
            variant="outline"
            size="lg"
            className="mt-4"
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default ManageAddressesScreen;
