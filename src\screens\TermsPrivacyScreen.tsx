import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../hooks/useTheme';

const TermsPrivacyScreen = () => {
  const navigation = useNavigation();
  const { isDark } = useTheme();
  const [activeTab, setActiveTab] = useState<'terms' | 'privacy'>('terms');

  const renderSection = (title: string, content: string[]) => (
    <View className="mb-6">
      <Text className={`text-lg font-bold mb-3 ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
        {title}
      </Text>
      {content.map((paragraph, index) => (
        <Text 
          key={index} 
          className={`text-sm leading-6 mb-3 ${isDark ? 'text-neutral-300' : 'text-neutral-600'}`}
        >
          {paragraph}
        </Text>
      ))}
    </View>
  );

  const termsContent = {
    acceptance: [
      "By accessing and using the GroceEase mobile application, you accept and agree to be bound by the terms and provision of this agreement."
    ],
    services: [
      "GroceEase provides an online platform for ordering groceries and food items for delivery or pickup.",
      "We reserve the right to modify, suspend, or discontinue any aspect of our services at any time without notice.",
      "All orders are subject to availability and confirmation of the order price."
    ],
    userAccount: [
      "You are responsible for maintaining the confidentiality of your account and password.",
      "You agree to accept responsibility for all activities that occur under your account.",
      "You must notify us immediately of any unauthorized use of your account."
    ],
    orders: [
      "All orders are subject to acceptance by GroceEase and our partner stores.",
      "Prices are subject to change without notice until the order is confirmed.",
      "We reserve the right to cancel orders due to product unavailability or other circumstances."
    ],
    liability: [
      "GroceEase shall not be liable for any indirect, incidental, special, or consequential damages.",
      "Our total liability shall not exceed the amount paid for the specific order in question."
    ]
  };

  const privacyContent = {
    collection: [
      "We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.",
      "We automatically collect certain information about your device and usage of our services."
    ],
    usage: [
      "We use your information to provide, maintain, and improve our services.",
      "To process transactions and send you related information.",
      "To send you technical notices, updates, security alerts, and support messages.",
      "To respond to your comments, questions, and customer service requests."
    ],
    sharing: [
      "We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.",
      "We may share your information with trusted partners who assist us in operating our services."
    ],
    security: [
      "We implement appropriate security measures to protect your personal information.",
      "However, no method of transmission over the internet is 100% secure.",
      "We cannot guarantee absolute security of your information."
    ],
    rights: [
      "You have the right to access, update, or delete your personal information.",
      "You may opt out of receiving promotional communications from us.",
      "You can contact us to exercise these rights or ask questions about our privacy practices."
    ]
  };

  return (
    <SafeAreaView className={`flex-1 ${isDark ? 'bg-neutral-900' : 'bg-neutral-50'}`}>
      {/* Header */}
      <View className={`px-4 py-4 border-b ${isDark ? 'bg-neutral-800 border-neutral-700' : 'bg-white border-neutral-200'}`}>
        <View className="flex-row items-center justify-between">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="w-10 h-10 items-center justify-center"
          >
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={isDark ? '#f5f5f5' : '#1e293b'} 
            />
          </TouchableOpacity>
          <Text className={`text-lg font-bold ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
            Terms & Privacy
          </Text>
          <View className="w-10" />
        </View>

        {/* Tab Selector */}
        <View className={`flex-row mt-4 p-1 rounded-xl ${isDark ? 'bg-neutral-700' : 'bg-neutral-100'}`}>
          <TouchableOpacity
            onPress={() => setActiveTab('terms')}
            className={`flex-1 py-2 px-4 rounded-lg ${
              activeTab === 'terms' 
                ? isDark ? 'bg-neutral-600' : 'bg-white shadow-sm' 
                : ''
            }`}
          >
            <Text className={`text-center text-sm font-medium ${
              activeTab === 'terms' 
                ? isDark ? 'text-neutral-100' : 'text-neutral-800'
                : isDark ? 'text-neutral-400' : 'text-neutral-600'
            }`}>
              Terms of Service
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setActiveTab('privacy')}
            className={`flex-1 py-2 px-4 rounded-lg ${
              activeTab === 'privacy' 
                ? isDark ? 'bg-neutral-600' : 'bg-white shadow-sm' 
                : ''
            }`}
          >
            <Text className={`text-center text-sm font-medium ${
              activeTab === 'privacy' 
                ? isDark ? 'text-neutral-100' : 'text-neutral-800'
                : isDark ? 'text-neutral-400' : 'text-neutral-600'
            }`}>
              Privacy Policy
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1 px-4 py-6" showsVerticalScrollIndicator={false}>
        {activeTab === 'terms' ? (
          <View>
            <Text className={`text-2xl font-bold mb-2 ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
              Terms of Service
            </Text>
            <Text className={`text-sm mb-6 ${isDark ? 'text-neutral-400' : 'text-neutral-600'}`}>
              Last updated: January 1, 2024
            </Text>

            {renderSection('1. Acceptance of Terms', termsContent.acceptance)}
            {renderSection('2. Description of Services', termsContent.services)}
            {renderSection('3. User Account', termsContent.userAccount)}
            {renderSection('4. Orders and Payments', termsContent.orders)}
            {renderSection('5. Limitation of Liability', termsContent.liability)}

            <View className={`rounded-2xl p-4 mt-6 ${isDark ? 'bg-info-900/20 border-info-800' : 'bg-info-50 border-info-200'} border`}>
              <View className="flex-row items-start">
                <Ionicons 
                  name="information-circle" 
                  size={20} 
                  color={isDark ? '#60a5fa' : '#3b82f6'} 
                  style={{ marginTop: 2 }} 
                />
                <View className="flex-1 ml-3">
                  <Text className={`text-sm font-medium ${isDark ? 'text-info-200' : 'text-info-800'}`}>
                    Questions?
                  </Text>
                  <Text className={`text-sm mt-1 ${isDark ? 'text-info-300' : 'text-info-700'}`}>
                    If you have any questions about these Terms of Service, please contact <NAME_EMAIL>
                  </Text>
                </View>
              </View>
            </View>
          </View>
        ) : (
          <View>
            <Text className={`text-2xl font-bold mb-2 ${isDark ? 'text-neutral-100' : 'text-neutral-800'}`}>
              Privacy Policy
            </Text>
            <Text className={`text-sm mb-6 ${isDark ? 'text-neutral-400' : 'text-neutral-600'}`}>
              Last updated: January 1, 2024
            </Text>

            {renderSection('1. Information We Collect', privacyContent.collection)}
            {renderSection('2. How We Use Your Information', privacyContent.usage)}
            {renderSection('3. Information Sharing', privacyContent.sharing)}
            {renderSection('4. Data Security', privacyContent.security)}
            {renderSection('5. Your Rights', privacyContent.rights)}

            <View className={`rounded-2xl p-4 mt-6 ${isDark ? 'bg-success-900/20 border-success-800' : 'bg-success-50 border-success-200'} border`}>
              <View className="flex-row items-start">
                <Ionicons 
                  name="shield-checkmark" 
                  size={20} 
                  color={isDark ? '#4ade80' : '#22c55e'} 
                  style={{ marginTop: 2 }} 
                />
                <View className="flex-1 ml-3">
                  <Text className={`text-sm font-medium ${isDark ? 'text-success-200' : 'text-success-800'}`}>
                    Your Privacy Matters
                  </Text>
                  <Text className={`text-sm mt-1 ${isDark ? 'text-success-300' : 'text-success-700'}`}>
                    We are committed to protecting your privacy and being transparent about how we use your data. Contact <NAME_EMAIL> for any concerns.
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default TermsPrivacyScreen;
