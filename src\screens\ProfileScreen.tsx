import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Switch,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useCart } from '../hooks/useCart';
import { useAuth } from '../hooks/useAuth';
import { useTheme } from '../hooks/useTheme';
import LoadingSpinner from '../components/LoadingSpinner';

const ProfileScreen = () => {
  const navigation = useNavigation();
  const { getCartSummary } = useCart();
  const { user, logout } = useAuth();
  const { isDark, themeMode, setThemeMode } = useTheme();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  const cartSummary = getCartSummary();

  // User data is now from auth context
  const isLoading = false; // Auth context handles loading

  const handleCartPress = () => {
    navigation.navigate('Cart' as never);
  };

  const handleEditProfile = () => {
    navigation.navigate('EditProfile' as never);
  };

  const handleOrderHistory = () => {
    navigation.navigate('OrderHistory' as never);
  };

  const handleAddresses = () => {
    navigation.navigate('ManageAddresses' as never);
  };

  const handlePaymentMethods = () => {
    navigation.navigate('PaymentMethods' as never);
  };

  const handleSupport = () => {
    Alert.alert(
      'Customer Support',
      'Need help? Contact us:\n\nPhone: +****************\nEmail: <EMAIL>\n\nOur support team is available 24/7 to assist you.',
      [{ text: 'OK' }]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'About GroceEase',
      'GroceEase v1.0.0\n\nFresh groceries delivered to your doorstep in under 2 hours. We source the finest organic and locally grown products to ensure quality and freshness.\n\n© 2024 GroceEase. All rights reserved.',
      [{ text: 'OK' }]
    );
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              // Navigation will be handled by auth state change
            } catch (error) {
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          }
        }
      ]
    );
  };

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightElement?: React.ReactNode,
    showChevron: boolean = true
  ) => (
    <TouchableOpacity
      className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 mb-3"
      onPress={onPress}
      disabled={!onPress}
    >
      <View className="flex-row items-center">
        <View className="w-10 h-10 bg-neutral-100 rounded-full items-center justify-center mr-4">
          <Ionicons name={icon as any} size={20} color="#64748b" />
        </View>
        <View className="flex-1">
          <Text className="text-base font-semibold text-neutral-800">
            {title}
          </Text>
          {subtitle && (
            <Text className="text-sm text-neutral-600 mt-1">
              {subtitle}
            </Text>
          )}
        </View>
        {rightElement || (showChevron && onPress && (
          <Ionicons name="chevron-forward" size={20} color="#94a3b8" />
        ))}
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return <LoadingSpinner message="Loading profile..." fullScreen />;
  }

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="px-4 py-6">
          {/* Profile Header */}
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
            <View className="flex-row items-center">
              <Image
                source={{
                  uri: user?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400'
                }}
                className="w-16 h-16 rounded-full"
              />
              <View className="flex-1 ml-4">
                <Text className="text-xl font-bold text-neutral-800">
                  {user?.name || 'Guest User'}
                </Text>
                <Text className="text-base text-neutral-600 mt-1">
                  {user?.email || '<EMAIL>'}
                </Text>
                <Text className="text-sm text-neutral-500 mt-1">
                  {user?.phone || '+****************'}
                </Text>
              </View>
              <TouchableOpacity
                className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center"
                onPress={handleEditProfile}
              >
                <Ionicons name="pencil" size={18} color="#22c55e" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Quick Stats */}
          <View className="flex-row space-x-3 mb-6">
            <TouchableOpacity
              className="flex-1 bg-white rounded-2xl p-4 shadow-sm border border-neutral-100"
              onPress={handleCartPress}
            >
              <View className="items-center">
                <View className="w-12 h-12 bg-primary-100 rounded-full items-center justify-center mb-2">
                  <Ionicons name="bag" size={24} color="#22c55e" />
                </View>
                <Text className="text-lg font-bold text-neutral-800">
                  {cartSummary.itemCount}
                </Text>
                <Text className="text-sm text-neutral-600">
                  Items in Cart
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-1 bg-white rounded-2xl p-4 shadow-sm border border-neutral-100"
              onPress={handleOrderHistory}
            >
              <View className="items-center">
                <View className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center mb-2">
                  <Ionicons name="receipt" size={24} color="#3b82f6" />
                </View>
                <Text className="text-lg font-bold text-neutral-800">
                  0
                </Text>
                <Text className="text-sm text-neutral-600">
                  Total Orders
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Account Settings */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-neutral-800 mb-4 px-2">
              Account
            </Text>
            {renderSettingItem(
              'person-outline',
              'Edit Profile',
              'Update your personal information',
              handleEditProfile
            )}
            {renderSettingItem(
              'location-outline',
              'Manage Addresses',
              'Add or edit delivery addresses',
              handleAddresses
            )}
            {renderSettingItem(
              'card-outline',
              'Payment Methods',
              'Manage your payment options',
              handlePaymentMethods
            )}
            {renderSettingItem(
              'time-outline',
              'Order History',
              'View your past orders',
              handleOrderHistory
            )}
          </View>

          {/* App Settings */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-neutral-800 mb-4 px-2">
              Preferences
            </Text>
            {renderSettingItem(
              'notifications-outline',
              'Push Notifications',
              'Receive order updates and offers',
              undefined,
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: '#e2e8f0', true: '#bbf7d0' }}
                thumbColor={notificationsEnabled ? '#22c55e' : '#94a3b8'}
              />,
              false
            )}
            {renderSettingItem(
              'moon-outline',
              'Theme Mode',
              `Currently: ${themeMode === 'system' ? 'System' : themeMode === 'dark' ? 'Dark' : 'Light'}`,
              () => {
                // Cycle through theme modes: light -> dark -> system -> light
                const nextMode = themeMode === 'light' ? 'dark' : themeMode === 'dark' ? 'system' : 'light';
                setThemeMode(nextMode);
              },
              <View className="flex-row items-center">
                <Text className="text-sm text-neutral-600 mr-2">
                  {themeMode === 'system' ? '🔄' : themeMode === 'dark' ? '🌙' : '☀️'}
                </Text>
                <Ionicons name="chevron-forward" size={20} color="#94a3b8" />
              </View>,
              false
            )}
          </View>

          {/* Support & Info */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-neutral-800 mb-4 px-2">
              Support & Info
            </Text>
            {renderSettingItem(
              'help-circle-outline',
              'Customer Support',
              'Get help with your orders',
              handleSupport
            )}
            {renderSettingItem(
              'information-circle-outline',
              'About GroceEase',
              'App version and information',
              handleAbout
            )}
            {renderSettingItem(
              'document-text-outline',
              'Terms & Privacy',
              'Read our terms and privacy policy',
              () => navigation.navigate('TermsPrivacy' as never)
            )}
          </View>

          {/* Logout */}
          <TouchableOpacity
            className="bg-red-50 rounded-2xl p-4 border border-red-200 mb-8"
            onPress={handleLogout}
          >
            <View className="flex-row items-center justify-center">
              <Ionicons name="log-out-outline" size={20} color="#ef4444" />
              <Text className="text-base font-semibold text-red-600 ml-2">
                Logout
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileScreen;
